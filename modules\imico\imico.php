<?php
/**
 * Module Name: DSTweaks imico Module
 * Description: A module for deploying specific DSTweaks for imico website.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_imico_Module {
    public function __construct() {
        if (!is_admin()) {
            add_action('wp_enqueue_scripts', array($this, 'dstweaks_enqueue_assets'), 999);
        }
    }

    /**
     * Enqueue assets for imico module
     *
     * @return void
     */
    public function dstweaks_enqueue_assets() {
        // Register and enqueue inline style
        wp_register_style('dstweaks-imico-style', false);
        wp_enqueue_style('dstweaks-imico-style');

        // Register and enqueue inline script (load after jQuery and Divi scripts)
        wp_register_script('dstweaks-imico-script', false, array('jquery', 'et-builder-modules-script'), null, true);
        wp_enqueue_script('dstweaks-imico-script');

/* === CSS CODE STARTS HERE (Put the code between "<<<CSS" and "CSS") === */
wp_add_inline_style('dstweaks-imico-style', <<<CSS
    /* Link disabler */
    .no-link > a {
        pointer-events: none;
        cursor: default;
    }
    /* Reverse stacking order of Divi row columns on small screens */
    @media (max-width: 980px) { /* adjust breakpoint as needed */
    .reverse-stack {
        display: flex !important;
        flex-direction: column-reverse !important;
    }
    }
    /* Align submenu dropdown to the right of parent menu item */
    .et_pb_menu .et_pb_menu__menu>nav>ul>li>ul {
        left: auto !important;
        right: 0 !important;
    }
    /* Move submenu arrow to the left without changing vertical alignment */
    .et-menu .menu-item-has-children > a::after {
        left: 0 !important;      /* force left side */
        right: auto !important;  /* remove right side */
    }

    /* Add padding so text doesn't overlap arrow */
    .et-menu .menu-item-has-children > a {
        padding-left: 20px;      /* adjust as needed */
    }
    .et-menu .sub-menu .menu-item-has-children > a {
        position: relative;
        padding-left: 20px; /* space for arrow */
    }
    /* Left arrow for deeper submenus, perfectly centered */
    .et-menu .sub-menu .menu-item-has-children > a::after {
        content: "\34" !important;           /* ETmodules left arrow */
        font-family: 'ETmodules' !important;
        position: absolute;
        left: 0 !important;
        right: auto !important;
        top: 0 !important;
        height: 100%;
        display: inline-flex;
        align-items: center;                 /* vertical centering */
        justify-content: center;
    }
    /* Make sub-submenus open to the left of their parent */
    .et-menu .sub-menu .sub-menu {
        left: auto !important;
        right: 100% !important;
    }
    .et-menu .sub-menu .sub-menu {
        left: auto !important;
        right: 100% !important;
        top: 0 !important;
    }
CSS
);
/* === END OF CSS CODE === */

/* === JS CODE STARTS HERE (Put the code between "<<<JS" and "JS") === */
wp_add_inline_script('dstweaks-imico-script', <<<'JS'

JS
);
/* === END OF JS CODE === */
    }
}

if (class_exists('DSTweaks_imico_Module') && !isset($GLOBALS['dstweaks_modules']['imico']['obj'])) {
    $GLOBALS['dstweaks_modules']['imico']['obj'] = new DSTweaks_imico_Module();
} else
    $GLOBALS['dstweaks_modules']['imico']['errors'] = "Instantiation failed. Couldn\'t find the required class.";