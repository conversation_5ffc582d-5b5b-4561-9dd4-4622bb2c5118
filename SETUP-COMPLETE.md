# DSTweaks Multi-Branch Setup Complete! 🎉

## What Was Accomplished

### ✅ Repository Restructuring Complete

Your DSTweaks plugin repository has been successfully transformed into a multi-branch architecture:

**Main Branch (`main`)**
- Contains ALL modules: cars, diviModules, imico, mvmDashti, persianRTLFixes, projects, shortcodes
- Serves as the central repository and source of truth
- Updated manifest.json with all module hashes and versions

**MVMDashti Branch (`mvmdashti`)**  
- Contains: cars, diviModules, mvmDashti, persianRTLFixes, shortcodes
- Specific to MVMDashti website
- Custom manifest.json with only relevant modules

**Imico Branch (`imico`)**
- Contains: diviModules, imico, persianRTLFixes, projects, shortcodes  
- Specific to imico website (renamed from imico-site)
- Custom manifest.json with only relevant modules

### ✅ Bidirectional Sync Workflow

**Automated Tools Created:**
- `workflow/sync-modules.ps1` - PowerShell automation script
- `workflow/sync.bat` - Simple batch file wrapper
- `.vscode/tasks.json` - VSCode task integration
- `workflow/README.md` - Comprehensive documentation

**Workflow Capabilities:**
- Push updates from main → sub-branches (selective by module)
- Pull updates from sub-branches → main
- Automatic propagation to other relevant branches
- Conflict detection and resolution guidance
- Branch status monitoring

### ✅ VSCode Integration

**Available Tasks (Ctrl+Shift+P → "Tasks: Run Task"):**
- "DSTweaks: Show Branch Status"
- "DSTweaks: Push Module to Sub-branches" 
- "DSTweaks: Pull from Sub-branch"
- "DSTweaks: Switch to [Branch]"

## How to Use Your New Setup

### Daily Development Workflow

1. **Working on a Common Module (e.g., shortcodes):**
   ```bash
   git checkout main
   # Make your changes
   git commit -m "Update shortcodes: add new feature"
   workflow/sync.bat push shortcodes
   ```

2. **Working on Site-Specific Module:**
   ```bash
   git checkout mvmdashti
   # Make changes to cars or mvmDashti module
   git commit -m "Update cars module: fix styling"
   workflow/sync.bat pull mvmdashti
   ```

3. **Checking Status:**
   ```bash
   workflow/sync.bat status
   ```

### VSCode Workflow

1. Use GitLens to see commit history and changes
2. Use Command Palette tasks for quick operations
3. GitLens will show you exactly which files changed in each commit
4. Use cherry-pick for selective updates

## Module Distribution Reference

| Module | main | mvmdashti | imico | Purpose |
|--------|------|-----------|-------|---------|
| cars | ✅ | ✅ | ❌ | MVMDashti car listings |
| diviModules | ✅ | ✅ | ✅ | Common Divi modules |
| imico | ✅ | ❌ | ✅ | imico specific features |
| mvmDashti | ✅ | ✅ | ❌ | MVMDashti specific features |
| persianRTLFixes | ✅ | ✅ | ✅ | Persian/RTL language fixes |
| projects | ✅ | ❌ | ✅ | Project management features |
| shortcodes | ✅ | ✅ | ✅ | Common shortcodes |

## Next Steps

1. **Test the Workflow:**
   - Try making a small change in main branch
   - Use `workflow/sync.bat push [module]` to propagate
   - Verify changes appear in relevant sub-branches

2. **Update Remote Repository:**
   ```bash
   git push origin main
   git push origin mvmdashti  
   git push origin imico
   ```

3. **Team Training:**
   - Share `workflow/README.md` with team members
   - Demonstrate the VSCode tasks
   - Practice the common workflows

## Troubleshooting

- **Conflicts:** The scripts will detect conflicts and provide guidance
- **Missing Modules:** Check the module distribution matrix above
- **Branch Issues:** Use `workflow/sync.bat status` to diagnose

## Support

- **Workflow Guide:** `workflow/README.md`
- **Script Help:** `workflow/sync.bat` (no arguments for usage)
- **VSCode Tasks:** Available in Command Palette

Your multi-branch DSTweaks setup is now ready for production use! 🚀
