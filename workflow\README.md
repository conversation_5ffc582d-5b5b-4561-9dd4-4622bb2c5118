# DSTweaks Multi-Branch Workflow Guide

## Branch Structure

### Main Branch
- **Purpose**: Contains ALL modules for the DSTweaks plugin
- **Modules**: cars, diviModules, imico, mvmDashti, persianRTLFixes, projects, shortcodes
- **Role**: Central repository for all module updates and source of truth

### Sub-Branches
1. **mvmdashti**: MVMDashti website specific modules
   - **Modules**: cars, diviModules, mvmDashti, persianRTLFixes, shortcodes
   
2. **imico**: imico website specific modules  
   - **Modules**: diviModules, imico, persianRTLFixes, projects, shortcodes

## Workflow Strategies

### 1. Updating Modules from Main → Sub-branches

When you update a module in the main branch, you can selectively push those changes to relevant sub-branches:

#### For Common Modules (diviModules, persianRTLFixes, shortcodes):
```bash
# Update in main branch first
git checkout main
# Make your changes to the module
git add modules/[module-name]
git commit -m "Update [module-name]: [description]"

# Push to mvmdashti branch
git checkout mvmdashti
git cherry-pick [commit-hash]  # or git merge main (if you want all changes)

# Push to imico branch  
git checkout imico
git cherry-pick [commit-hash]  # or git merge main (if you want all changes)
```

#### For Site-Specific Modules:
```bash
# For mvmDashti-specific modules (cars, mvmDashti):
git checkout main
# Make changes
git commit -m "Update [module]: [description]"

# Push only to mvmdashti branch
git checkout mvmdashti
git cherry-pick [commit-hash]

# For imico-specific modules (imico, projects):
git checkout main  
# Make changes
git commit -m "Update [module]: [description]"

# Push only to imico branch
git checkout imico
git cherry-pick [commit-hash]
```

### 2. Updating Modules from Sub-branches → Main

When you make changes in a sub-branch, you should push them back to main and then to other relevant sub-branches:

```bash
# Make changes in sub-branch
git checkout mvmdashti  # or imico
# Make your changes
git add .
git commit -m "Update [module]: [description]"

# Push to main branch
git checkout main
git cherry-pick [commit-hash]  # or git merge mvmdashti

# If the module exists in other sub-branches, update them too
git checkout imico  # if the module exists in imico
git cherry-pick [commit-hash]
```

## Module Distribution Matrix

| Module | Main | mvmdashti | imico |
|--------|------|-----------|-------|
| cars | ✅ | ✅ | ❌ |
| diviModules | ✅ | ✅ | ✅ |
| imico | ✅ | ❌ | ✅ |
| mvmDashti | ✅ | ✅ | ❌ |
| persianRTLFixes | ✅ | ✅ | ✅ |
| projects | ✅ | ❌ | ✅ |
| shortcodes | ✅ | ✅ | ✅ |

## VSCode + GitLens Integration

### Viewing Changes
- GitLens will show you exactly which files changed in each commit
- Use the GitLens sidebar to see commit history and file changes
- Compare branches to see differences

### Making Selective Updates
1. Use GitLens to identify which modules were changed
2. Use cherry-pick for selective module updates
3. Use merge for full branch synchronization

### Conflict Resolution
- When conflicts occur, VSCode will highlight them
- Resolve conflicts manually in the editor
- Use GitLens to see the history of conflicting changes

## Best Practices

1. **Always update main first** for new features
2. **Use descriptive commit messages** that specify which module was changed
3. **Test changes** in the target environment before pushing to sub-branches
4. **Keep manifest.json files separate** for each branch
5. **Use cherry-pick for selective updates** rather than full merges when possible

## Quick Commands Reference

```bash
# Switch branches
git checkout main|mvmdashti|imico

# See all branches
git branch -a

# Cherry-pick specific commit
git cherry-pick [commit-hash]

# See commit history
git log --oneline

# See changes in a commit
git show [commit-hash]

# Compare branches
git diff main..mvmdashti
git diff main..imico
```
