@echo off
REM DSTweaks Module Sync Helper
REM Usage examples:
REM   sync.bat status
REM   sync.bat push shortcodes
REM   sync.bat pull mvmdashti

if "%1"=="status" (
    powershell -ExecutionPolicy Bypass -File "%~dp0sync-modules.ps1" -Action status
) else if "%1"=="push" (
    if "%2"=="" (
        echo Usage: sync.bat push [module-name] [optional-commit-hash]
        echo Available modules: cars, diviModules, imico, mvmDashti, persianRTLFixes, projects, shortcodes
    ) else (
        powershell -ExecutionPolicy Bypass -File "%~dp0sync-modules.ps1" -Action push-to-subs -Module %2 -CommitHash %3
    )
) else if "%1"=="pull" (
    if "%2"=="" (
        echo Usage: sync.bat pull [source-branch] [optional-commit-hash]
        echo Available branches: mvmdashti, imico
    ) else (
        powershell -ExecutionPolicy Bypass -File "%~dp0sync-modules.ps1" -Action pull-from-sub -SourceBranch %2 -CommitHash %3
    )
) else (
    echo DSTweaks Module Sync Helper
    echo.
    echo Usage:
    echo   sync.bat status                           - Show branch status
    echo   sync.bat push [module] [commit-hash]      - Push module from main to sub-branches
    echo   sync.bat pull [branch] [commit-hash]      - Pull changes from sub-branch to main
    echo.
    echo Examples:
    echo   sync.bat status
    echo   sync.bat push shortcodes
    echo   sync.bat push diviModules abc1234
    echo   sync.bat pull mvmdashti
    echo   sync.bat pull imico def5678
)
