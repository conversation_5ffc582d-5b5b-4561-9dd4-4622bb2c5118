<?php
/**
 * Module Name: DSTweaks Project Module
 * Description: A module for managing project-related functionalities in DSTweaks plugin.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Projects_Module {
    // Unified custom fields array with translatable labels
    private $project_fields = [
        // Number fields
        [ 'slug' => 'Progress', 'label' => 'Progress', 'type' => 'number', 'label_trans' => 'Progress' ],
        // Text fields
        // Textarea fields
        // Image fields
        // Gallery field
        // File field
    ];

    public function __construct() {
        add_action('init', array($this, 'register_project_meta'));
        add_action('do_meta_boxes', array($this, 'remove_default_meta_boxes'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('add_meta_boxes', array($this, 'add_project_meta_boxes'), 10);
        add_action('save_post', array($this, 'save_project_meta'));
    }

    /**
     * Register custom meta fields for projects
     *
     * @return void
     */
    public function register_project_meta() {
        foreach ($this->project_fields as $field) {
            $args = [
                'single' => true,
                'show_in_rest' => true,
            ];
            switch ($field['type']) {
                case 'number':
                    $args['type'] = 'number';
                    break;
                case 'text':
                    $args['type'] = 'string';
                    break;
                case 'textarea':
                    $args['type'] = 'string';
                    $args['sanitize_callback'] = 'wp_kses_post';
                    break;
                case 'image':
                case 'file':
                    $args['type'] = 'number';
                    break;
                case 'gallery':
                    $args['type'] = 'string';
                    $args['sanitize_callback'] = 'sanitize_text_field';
                    break;
            }
            register_meta('post', $field['slug'], $args);
        }
    }

    /**
     * Remove unwanted default meta boxes
     *
     * @return void
     */
    public function remove_default_meta_boxes() {
        // Remove the author meta box
        remove_meta_box('authordiv', 'project', 'normal');
        
        // Remove the slug meta box
        remove_meta_box('slugdiv', 'project', 'normal');
        
        // Remove the custom fields meta box
        remove_meta_box('postcustom', 'project', 'normal');
        
        // Remove the revisions meta box
        remove_meta_box('revisionsdiv', 'project', 'normal');
    }

    /**
     * Enqueue admin scripts for media and editor
     *
     * @param string $hook Current admin page hook
     * @return void
     */
    public function enqueue_admin_scripts($hook) {
        if ('post.php' != $hook && 'post-new.php' != $hook) {
            return;
        }
        wp_enqueue_media();
        wp_enqueue_editor();
    }

    /**
     * Add meta boxes for project details
     *
     * @return void
     */
    public function add_project_meta_boxes() {
        // Progress group
        add_meta_box(
            'project_progress',
            __('Project Progress', 'dstweaks'),
            array($this, 'render_progress_meta_box'),
            'project',
            'normal',
            'high'
        );
    }
    /**
     * Progress Section
     *
     * @param WP_Post $post Current post object
     * @return void
     */
    public function render_progress_meta_box($post) {
        wp_nonce_field('project_meta_box', 'project_meta_box_nonce');
        ?>
        <div class="project-meta-box">
            <?php
            foreach ($this->project_fields as $field) {
                if (in_array($field['slug'], ['progress'])) {
                    $value = get_post_meta($post->ID, $field['slug'], true);
                    $type = $field['type'] === 'number' ? 'number' : 'text';
                    ?>
                    <p>
                        <label for="<?php echo esc_attr($field['slug']); ?>"><?php echo esc_html(__($field['label_trans'], 'dstweaks')); ?>:</label>
                        <input type="<?php echo $type; ?>" id="<?php echo esc_attr($field['slug']); ?>" name="<?php echo esc_attr($field['slug']); ?>" value="<?php echo esc_attr($value); ?>">
                    </p>
                    <?php
                }
            }
            ?>
        </div>
        <?php
    }

    // Public method to get field data by slug for a given post
    public function get_field_data_by_slug($post_id, $slug) {
        // Optionally, validate slug exists in $this->project_fields
        $valid = false;
        foreach ($this->project_fields as $field) {
            if ($field['slug'] === $slug) {
                $valid = true;
                break;
            }
        }
        if (!$valid) {
            return null; // or throw an exception if you prefer
        }
        return get_post_meta($post_id, $slug, true);
    }

    // Public method to get the project_fields array data by slug
    public function get_field_info_by_slug($slug) {
        foreach ($this->project_fields as $field) {
            if ($field['slug'] === $slug) {
                return $field;
            }
        }
        return null;
    }

    /**
     * Save the project meta box data
     *
     * @param int $post_id Post ID
     * @return void
     */
    public function save_project_meta($post_id) {
        if (!isset($_POST['project_project_box_nonce']) || !wp_verify_nonce($_POST['project_meta_box_nonce'], 'project_meta_box')) {
            return;
        }
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        foreach ($this->project_fields as $field) {
            $slug = $field['slug'];
            if (!isset($_POST[$slug])) continue;
            $value = $_POST[$slug];
            switch ($field['type']) {
                case 'number':
                case 'image':
                case 'file':
                    update_post_meta($post_id, $slug, absint($value));
                    break;
                case 'text':
                    update_post_meta($post_id, $slug, sanitize_text_field($value));
                    break;
                case 'textarea':
                    update_post_meta($post_id, $slug, wp_kses_post($value));
                    break;
                case 'gallery':
                    $gallery = preg_replace('/[^0-9,]/', '', trim($value));
                    update_post_meta($post_id, $slug, $gallery);
                    break;
            }
        }
    }
}

if (class_exists('DSTweaks_Projects_Module') && !isset($GLOBALS['dstweaks_modules']['projects']['obj'])) {
    $GLOBALS['dstweaks_modules']['projects']['obj'] = new DSTweaks_Projects_Module();
} else 
    $GLOBALS['dstweaks_modules']['projects']['errors'] = "Instantiation failed. Couldn\'t find the required class.";