# DSTweaks — WordPress utility plugin

DSTweaks is a modular WordPress plugin that provides site-specific tweaks and Divi-compatible dynamic modules. It uses a multi-branch architecture to support different websites with shared and site-specific modules.

## Multi-Branch Architecture

This repository uses a specialized branching strategy to support multiple websites:

### Branch Structure
- **`main`** - Contains ALL modules (master repository)
- **`mvmdashti`** - MVMDashti website specific modules
- **`imico`** - imico website specific modules

### Module Distribution
| Module | main | mvmdashti | imico |
|--------|------|-----------|-------|
| cars | ✅ | ✅ | ❌ |
| diviModules | ✅ | ✅ | ✅ |
| imico | ✅ | ❌ | ✅ |
| mvmDashti | ✅ | ✅ | ❌ |
| persianRTLFixes | ✅ | ✅ | ✅ |
| projects | ✅ | ❌ | ✅ |
| shortcodes | ✅ | ✅ | ✅ |

## Key Features
- **Modular Architecture**: Each module is self-contained with its own functionality
- **Bidirectional Sync**: Updates can flow from main to sub-branches and vice versa
- **Selective Updates**: Only relevant modules are updated in each branch
- **Automated Workflow**: PowerShell scripts and VSCode tasks for easy management

## Quick Start

### For Developers
1. Clone the appropriate branch for your website:
   ```bash
   # For MVMDashti website
   git clone -b mvmdashti <repository-url> DSTweaks

   # For imico website
   git clone -b imico <repository-url> DSTweaks

   # For development (all modules)
   git clone -b main <repository-url> DSTweaks
   ```

2. Copy to WordPress plugins directory and activate

### For Module Updates
Use the provided workflow tools in the `workflow/` directory:

```bash
# Show branch status
workflow/sync.bat status

# Push module from main to sub-branches
workflow/sync.bat push shortcodes

# Pull changes from sub-branch to main
workflow/sync.bat pull mvmdashti
```

Or use VSCode tasks: `Ctrl+Shift+P` → "Tasks: Run Task" → "DSTweaks: ..."

## Development notes
- **Code layout**:
  - `DSTweaks.php` — plugin bootstrap and hooks.
  - `modules/` — modular features (Divi modules, shortcodes, etc.).
  - `common/` — admin helpers and shared code.
  - `languages/` — translation `.po`/`.mo` files.
- **Standards**:
  - Keep PHP files compatible with your target PHP version used on your host.
  - Avoid committing credentials. If you must use secrets locally, place them in an ignored `.env` and load them at runtime.

## Workflow Documentation

For detailed workflow instructions, see:
- **[workflow/README.md](workflow/README.md)** - Comprehensive workflow guide
- **[.vscode/tasks.json](.vscode/tasks.json)** - VSCode task definitions
- **[workflow/sync-modules.ps1](workflow/sync-modules.ps1)** - PowerShell automation script

### Common Workflows

**Updating a Common Module (e.g., shortcodes):**
1. Make changes in `main` branch
2. Run `workflow/sync.bat push shortcodes` to update all relevant branches

**Updating a Site-Specific Module:**
1. Make changes in the appropriate sub-branch
2. Run `workflow/sync.bat pull [branch-name]` to sync to main
3. Optionally propagate to other branches if applicable

**Adding a New Branch:**
1. Create branch from main: `git checkout -b newsite`
2. Remove unwanted modules and update `modules/manifest.json`
3. Update workflow scripts with new branch information

## Security checklist before pushing
- Remove any hard-coded passwords, API keys, or database credentials.
- Ensure `wp-config.php` and any local `.env` are listed in `.gitignore`.
- Use Personal Access Tokens (PAT) or SSH keys for remote auth; don't place them in the repo.

## Testing and debugging
- Use `WP_DEBUG` in your local `wp-config.php` to surface PHP warnings during development.
- Review `languages/` files when changing user-visible strings.

## Support & contact
- This README documents the plugin layout. For questions about this repository contact the code owner directly.

## License
- Add a LICENSE file if you want to publish with an explicit license. If private, you can skip this.

## Changelog
- 1.0 — initial local codebase
