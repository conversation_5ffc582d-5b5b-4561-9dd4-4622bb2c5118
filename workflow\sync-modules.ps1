# DSTweaks Module Sync Helper Script
# This script helps synchronize module updates between branches

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("push-to-subs", "pull-from-sub", "status")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [string]$Module,
    
    [Parameter(Mandatory=$false)]
    [string]$CommitHash,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("mvmdashti", "imico")]
    [string]$SourceBranch
)

# Module distribution matrix
$ModuleMatrix = @{
    "cars" = @("main", "mvmdashti")
    "diviModules" = @("main", "mvmdashti", "imico")
    "imico" = @("main", "imico")
    "mvmDashti" = @("main", "mvmdashti")
    "persianRTLFixes" = @("main", "mvmdashti", "imico")
    "projects" = @("main", "imico")
    "shortcodes" = @("main", "mvmdashti", "imico")
}

function Show-Status {
    Write-Host "=== DSTweaks Branch Status ===" -ForegroundColor Green
    Write-Host ""
    
    # Show current branch
    $currentBranch = git branch --show-current
    Write-Host "Current Branch: $currentBranch" -ForegroundColor Yellow
    Write-Host ""
    
    # Show all branches
    Write-Host "Available Branches:" -ForegroundColor Cyan
    git branch -a
    Write-Host ""
    
    # Show recent commits for each branch
    $branches = @("main", "mvmdashti", "imico")
    foreach ($branch in $branches) {
        Write-Host "Recent commits in ${branch}:" -ForegroundColor Magenta
        git log $branch --oneline -5
        Write-Host ""
    }
}

function Push-ToSubBranches {
    param($ModuleName, $Hash)
    
    if (-not $ModuleName) {
        Write-Host "Module name is required for push-to-subs action" -ForegroundColor Red
        return
    }
    
    if (-not $ModuleMatrix.ContainsKey($ModuleName)) {
        Write-Host "Unknown module: $ModuleName" -ForegroundColor Red
        Write-Host "Available modules: $($ModuleMatrix.Keys -join ', ')" -ForegroundColor Yellow
        return
    }
    
    $targetBranches = $ModuleMatrix[$ModuleName] | Where-Object { $_ -ne "main" }
    
    if ($targetBranches.Count -eq 0) {
        Write-Host "Module $ModuleName is only in main branch" -ForegroundColor Yellow
        return
    }
    
    Write-Host "Pushing module '$ModuleName' to branches: $($targetBranches -join ', ')" -ForegroundColor Green
    
    # Ensure we're on main branch
    git checkout main
    
    if ($Hash) {
        $commitToUse = $Hash
    } else {
        # Get the latest commit that modified the module
        $commitToUse = git log -1 --format="%H" -- "modules/$ModuleName"
        if (-not $commitToUse) {
            Write-Host "No commits found for module $ModuleName" -ForegroundColor Red
            return
        }
    }
    
    Write-Host "Using commit: $commitToUse" -ForegroundColor Cyan
    
    foreach ($branch in $targetBranches) {
        Write-Host "Updating branch: $branch" -ForegroundColor Yellow
        git checkout $branch
        
        # Check if cherry-pick will cause conflicts
        git cherry-pick --no-commit $commitToUse 2>&1 | Out-Null
        if ($LASTEXITCODE -eq 0) {
            git commit --no-edit
            Write-Host "Successfully updated $branch" -ForegroundColor Green
        } else {
            git cherry-pick --abort 2>$null
            Write-Host "Conflict detected in $branch. Manual resolution required." -ForegroundColor Red
            Write-Host "Run: git checkout $branch && git cherry-pick $commitToUse" -ForegroundColor Yellow
        }
    }
    
    # Return to main
    git checkout main
}

function Get-FromSubBranch {
    param($SourceBranchName, $Hash)
    
    if (-not $SourceBranchName) {
        Write-Host "Source branch is required for pull-from-sub action" -ForegroundColor Red
        return
    }
    
    Write-Host "Pulling changes from $SourceBranchName to main" -ForegroundColor Green
    
    # Ensure we're on main branch
    git checkout main
    
    if ($Hash) {
        $commitToUse = $Hash
    } else {
        # Get the latest commit from source branch
        $commitToUse = git log -1 --format="%H" $SourceBranchName
    }
    
    Write-Host "Using commit: $commitToUse" -ForegroundColor Cyan
    
    # Cherry-pick the commit to main
    git cherry-pick --no-commit $commitToUse 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        git commit --no-edit
        Write-Host "Successfully pulled changes to main" -ForegroundColor Green
        
        # Ask if user wants to propagate to other branches
        $response = Read-Host "Do you want to propagate these changes to other relevant branches? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            # Detect which modules were changed and propagate
            $changedFiles = git diff-tree --no-commit-id --name-only -r $commitToUse
            $changedModules = $changedFiles | Where-Object { $_ -like "modules/*" } | ForEach-Object { 
                ($_ -split "/")[1] 
            } | Select-Object -Unique
            
            foreach ($module in $changedModules) {
                if ($ModuleMatrix.ContainsKey($module)) {
                    Write-Host "Propagating module: $module" -ForegroundColor Cyan
                    Push-ToSubBranches -ModuleName $module -Hash $commitToUse
                }
            }
        }
    } else {
        git cherry-pick --abort 2>$null
        Write-Host "Conflict detected. Manual resolution required." -ForegroundColor Red
        Write-Host "Run: git cherry-pick $commitToUse" -ForegroundColor Yellow
    }
}

# Main script logic
switch ($Action) {
    "status" {
        Show-Status
    }
    "push-to-subs" {
        Push-ToSubBranches -ModuleName $Module -Hash $CommitHash
    }
    "pull-from-sub" {
        Get-FromSubBranch -SourceBranchName $SourceBranch -Hash $CommitHash
    }
}
